import React from 'react';

import { H1, P } from '@/components/ui';
import { DotStepIndicator, type DotStepIndicatorProps } from '../ui';

type AuthLayoutProps = {
  title: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  pagination?: DotStepIndicatorProps;
};

export const AuthLayout = ({
  title,
  subTitle,
  children,
  footer,
  pagination,
}: AuthLayoutProps) => {
  return (
    <div className="flex flex-col flex-1 px-4 gap-6 mt-4">
      <div className="flex flex-row items-center justify-center py-6">
        {pagination && (
          <DotStepIndicator
            activeStep={pagination.activeStep}
            totalSteps={pagination.totalSteps}
          />
        )}
      </div>

      <div>
        <div className={'flex flex-1 flex-col gap-2 max-w-md w-md mx-auto'}>
          <div className="w-fit">
            {title && <H1>{title}</H1>}
            {subTitle && (
              <P className="text-grey-60 dark:text-grey-50">{subTitle}</P>
            )}
          </div>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col">
        {children}
        {footer && <div className="mt-auto">{footer}</div>}
      </div>
    </div>
  );
};
