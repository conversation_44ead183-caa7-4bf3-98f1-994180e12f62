'use client';

import React, { useMemo } from 'react';
import ReactQuill from 'react-quill-new';

import { SmRegularLabel } from '@/components/ui';
import { useTheme } from 'next-themes';

const defaultModules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline'],
    ['link', 'image'],
  ],
};

type RichTextInputProps = {
  value: string;
  onChange: (val: string) => void;
  label?: string;
  placeholder?: string;
  maxLength?: number;
  modules?: any;
};

const getPlainTextLength = (html: string): number => {
  if (typeof document === 'undefined') return 0;
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent?.length || 0;
};

export const RichTextInput: React.FC<RichTextInputProps> = ({
  value,
  onChange,
  label = 'Description',
  placeholder = 'Type here...',
  maxLength = 500,
  modules = defaultModules,
}) => {
  const plainTextLength = useMemo(
    () => getPlainTextLength(value || ''),
    [value]
  );
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const handleChange = (newValue: string) => {
    const length = getPlainTextLength(newValue);
    if (length <= maxLength) {
      onChange(newValue);
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <div>
          <span className="text-fg-muted-light dark:text-fg-muted-dark">
            {label}
          </span>
        </div>
      )}

      <div className="relative">
        <ReactQuill
          value={value}
          placeholder={placeholder}
          onChange={handleChange}
          modules={modules}
          className="quill-editor"
          style={
            {
              '--quill-bg': 'transparent',
              '--quill-border': isDark ? '#53575a' : '#e8ebeb',
              '--quill-text': isDark ? '#fff' : '#000',
              '--quill-placeholder': isDark ? '#898d8f' : '#6e7375',
            } as React.CSSProperties
          }
        />
      </div>

      <div className="flex justify-end items-center">
        <SmRegularLabel
          className={`${
            plainTextLength > maxLength * 0.9
              ? 'text-fg-danger-light dark:text-fg-danger-dark'
              : 'text-fg-muted-light dark:text-fg-muted-dark'
          }`}
        >
          {plainTextLength}/{maxLength}
        </SmRegularLabel>
      </div>
    </div>
  );
};
