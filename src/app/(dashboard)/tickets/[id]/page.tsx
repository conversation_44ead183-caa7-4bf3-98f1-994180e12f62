'use client';

import * as React from 'react';
import html2canvas from 'html2canvas-pro';
import { useSearchParams, useParams } from 'next/navigation';

import { ITicketExtension } from '@/api/events';
import { useGetUserTickets, IEventsWithTicket } from '@/api';
import { TicketCarousel } from '@/components/carousels';
import { EmptyState, LgBoldLabel, P, colors } from '@/components/ui';
import { removeSpacesAndSymbols, useAuth } from '@/lib';
import { Download } from 'lucide-react';
import { LoadingScreen } from '@/components/loaders';
import { toast } from 'react-hot-toast';

export default function EventTicket() {
  const { user } = useAuth();
  const [isCapturing, setIsCapturing] = React.useState(false);

  const searchParams = useSearchParams();
  const { id } = useParams<{ id: string }>();
  const transactionId = searchParams.get('transactionId');

  const { data: ticketsData, isLoading } = useGetUserTickets({
    variables: { id: user?.id || '' },
    enabled: !!user?.id,
  });

  const mergedTickets = React.useCallback(
    (eventsWithTicket?: IEventsWithTicket[]): ITicketExtension[] => {
      if (!eventsWithTicket) return [];
      return eventsWithTicket.reduce((tickets, event) => {
        const eventTickets = event.tickets
          .filter((ticket) => ticket.userId === user?.id)
          .map((ticket) => ({
            ...ticket,
            title: event.title,
            bannerUrl: event.bannerUrl,
            startTime: event.startTime,
            location: event.location,
            organizer: event.organizer,
            eventId: event.id,
            event,
            user: ticket.user,
          }));

        return [...tickets, ...eventTickets];
      }, [] as ITicketExtension[]);
    },
    [user?.id]
  );

  const ticketsList = React.useMemo(
    () => mergedTickets(ticketsData?.eventsWithTicket),
    [ticketsData?.eventsWithTicket, mergedTickets]
  );

  const displayTickets = React.useMemo(() => {
    if (transactionId) {
      return ticketsList.filter(
        ({ transactionId: txId }) => txId === transactionId
      );
    } else if (id) {
      const singleTicket = ticketsList.find(
        ({ id: ticketId }) => ticketId === id
      );
      return singleTicket ? [singleTicket] : [];
    }
    return ticketsList;
  }, [ticketsList, transactionId, id]);

  const [currentIndex, setCurrentIndex] = React.useState(0);
  const currentTicket = displayTickets[currentIndex];

  const [viewShotRefs, setViewShotRefs] = React.useState<
    React.RefObject<HTMLDivElement>[]
  >([]);

  React.useEffect(() => {
    setViewShotRefs((refs) =>
      Array(displayTickets.length)
        .fill(null)
        .map((_, i) => refs[i] || React.createRef<HTMLDivElement>())
    );
  }, [displayTickets.length]);

  const handleTicketChange = (index: number) => setCurrentIndex(index);

  const captureAndShare = async () => {
    const currentRef = viewShotRefs[currentIndex];
    if (!currentRef?.current || !currentTicket) {
      toast.error('Unable to capture ticket...');
      return;
    }

    try {
      setIsCapturing(true);

      const canvas = await html2canvas(currentRef.current, { scale: 2 });

      const blob = await new Promise<Blob | null>((resolve) =>
        canvas.toBlob((b) => resolve(b), 'image/jpeg', 0.9)
      );
      if (!blob) throw new Error('Failed to generate image blob');

      const fileName = removeSpacesAndSymbols(
        currentTicket.title.toLowerCase()
      );
      const finalFileName = `${fileName}_${Date.now()}.jpg`;

      const file = new File([blob], finalFileName, { type: 'image/jpeg' });

      if (navigator.canShare && navigator.canShare({ files: [file] })) {
        await navigator.share({
          files: [file],
          title: currentTicket.title,
        });
      } else {
        const objectUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = objectUrl;
        link.download = finalFileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(objectUrl);
      }
    } catch (error) {
      toast.error('Failed to capture ticket. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  if (isLoading) return <LoadingScreen />;

  if (!displayTickets.length) {
    return (
      <div className="flex flex-1 h-full items-center justify-center p-4">
        <EmptyState />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen gap-8 pb-12">
      <div className="h-16 flex items-center justify-between px-2 border-b">
        <div className="flex items-center gap-2">
          <LgBoldLabel>Ticket details</LgBoldLabel>
          {displayTickets.length > 1 && (
            <div className="rounded-full bg-brand-10 px-2 py-1">
              <P className="text-brand-60 dark:text-brand-60">
                {currentIndex + 1} of {displayTickets.length}
              </P>
            </div>
          )}
        </div>
        <button onClick={captureAndShare} disabled={isCapturing}>
          {isCapturing ? (
            <div className="animate-spin border-2 border-t-transparent rounded-full w-6 h-6 border-brand-60" />
          ) : (
            <Download size={28} color={colors.brand[60]} />
          )}
        </button>
      </div>

      <TicketCarousel
        tickets={displayTickets}
        onTicketChange={handleTicketChange}
        currentIndex={currentIndex}
        viewShotRefs={viewShotRefs}
      />
    </div>
  );
}
