'use client';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { type IEventCategories, useGetEventCategories } from '@/api/events';
import { ConfirmationDialog } from '@/components/dialogs';
import { CollaboratorCard } from '@/components/events';
import { CreateEventLayout } from '@/components/layouts';
import {
  ControlledSelect,
  DateTimePicker,
  InputPickerButton,
  P,
  PillOption,
  Spinner,
} from '@/components/ui';
import { Button, ControlledInput, Modal, colors } from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';
import TIMEZONES_JSON from '@/lib/timezones.json';
import { formatDateTime } from '@/lib';
import { useTheme } from 'next-themes';
import { type StepProps } from '../page';
import { IoAdd } from 'react-icons/io5';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';

const modules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline'],
    ['link', 'image'],
  ],
};

const timezones = TIMEZONES_JSON.map((timezone) => ({
  label: timezone.text,
  value: timezone.value,
  key: `${timezone.value}-${timezone.text}`,
}));

export default function AddDetails({ setStep, fromStep }: StepProps) {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const { data: categories, isLoading: categoriesLoading } =
    useGetEventCategories();

  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const selectedCategory = watch('category');
  const artists = watch('collaborators') || [];
  const startDatetime = watch('startDatetime');
  const endDatetime = watch('endDatetime');

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const handleStartDateChange = (date: Date) => {
    if (new Date() > date) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue('startDatetime', date);
  };

  const handleEndDateChange = (date: Date) => {
    const start = new Date(watch('startDatetime'));
    if (!start || new Date(start) > date) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue('endDatetime', date);
  };

  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'title',
    'description',
    'timezone',
    'category',
    'startDatetime',
    'endDatetime',
    'collaborators',
  ]);

  const selectCategory = useCallback(
    (category: IEventCategories) => {
      const currentCategory = watch('category');
      if (currentCategory === category.id) {
        setValue('category', '');
      } else {
        setValue('category', category.id);
      }
    },
    [setValue, watch]
  );

  const confirmCategory = useCallback(() => {
    setCategoryModalOpen(false);
  }, []);

  const categoryLabel = categories?.find(
    (c) => c.id === selectedCategory
  )?.category;

  const [categoryModalOpen, setCategoryModalOpen] = useState(false);

  return (
    <CreateEventLayout
      title="Tell us about your event"
      subTitle="Add details to make your event stand out."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(0)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={
              !fieldStates.title.isValid ||
              !fieldStates.description.isValidOptional ||
              !fieldStates.collaborators.isValidOptional ||
              !fieldStates.startDatetime.isValid ||
              !fieldStates.endDatetime.isValid ||
              !fieldStates.timezone.isValid ||
              !fieldStates.category.isValid
            }
            onPress={() => setStep(fromStep === 13 ? 13 : 2)}
          />
        </div>
      }
    >
      <ControlledInput name="title" label="Event title" control={control} />
      <ReactQuill
        value={watch('description')}
        placeholder="Describe your event"
        onChange={(value) => setValue('description', value)}
        modules={modules}
      />

      <DateTimePicker
        selected={startDatetime}
        onChange={handleStartDateChange}
        minDate={new Date()}
        label={startDatetimeLabel}
      />
      <DateTimePicker
        selected={endDatetime}
        onChange={handleEndDateChange}
        minDate={startDatetime}
        label={endDatetimeLabel}
      />
      <ControlledSelect
        control={control}
        name="timezone"
        placeholder="Timezone"
        options={timezones}
      />
      <InputPickerButton
        value={selectedCategory}
        label={categoryLabel || 'Category'}
        onClick={() => setCategoryModalOpen(true)}
        withChevron
      />
      {artists.length < 1 && (
        <Button
          label="Add artiste +"
          variant="secondary"
          className="w-[154px] py-2"
          size="sm"
          onPress={() => setStep(14)}
        />
      )}
      {artists.length > 0 && (
        <div className="space-y-4 overflow-visible">
          <P>Lineup</P>
          <div className="flex flex-row items-center gap-3">
            {artists.map((artist) => (
              <CollaboratorCard
                key={artist.id}
                artist={artist}
                isCreation
                onRemove={() => {
                  setArtistToDelete(artist.id);
                  setConfirmVisible(true);
                }}
              />
            ))}
            <div className="h-[92px] w-20 flex items-center justify-center">
              <div
                className="size-12 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark cursor-pointer"
                onClick={() => setStep(14)}
                aria-label="Add collaborator"
              >
                <IoAdd
                  size={32}
                  color={isDark ? colors.brand[40] : colors.brand[70]}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to remove this artist from your lineup?"
        onCancel={() => {
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            setTimeout(() => {
              setValue(
                'collaborators',
                artists.filter((a) => a.id !== artistToDelete)
              );
            }, 0);
          }
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
      />
      <Modal
        isOpen={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
        title="Select event category"
      >
        {categoriesLoading ? (
          <Spinner />
        ) : (
          <div className="flex-1 space-y-4 p-4">
            <div className="flex flex-wrap gap-2">
              {categories?.map((category) => (
                <PillOption
                  key={category.id}
                  option={category.category}
                  selected={selectedCategory === category.id}
                  onPress={() => selectCategory(category)}
                />
              ))}
            </div>

            <div className="mt-auto pb-4">
              <Button
                label="Confirm"
                disabled={!selectedCategory}
                onClick={confirmCategory}
              />
            </div>
          </div>
        )}
      </Modal>
    </CreateEventLayout>
  );
}
