{"name": "popla-webapp", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix && pnpm format", "lint:strict": "eslint . --max-warnings=0", "typecheck": "tsc --noEmit --incremental false", "test:watch": "jest --watch", "test": "jest", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@next/bundle-analyzer": "^15.5.4", "@paystack/inline-js": "^2.22.7", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-google-maps/api": "^2.20.7", "@rn-primitives/radio-group": "^1.2.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.90.2", "@vis.gl/react-google-maps": "^1.5.5", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.18", "embla-carousel-react": "^8.6.0", "eslint-plugin-react-hooks": "^5.2.0", "firebase": "^12.3.0", "framer-motion": "^12.23.22", "html2canvas-pro": "^1.5.11", "ky": "^1.11.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.539.0", "moment": "^2.30.1", "next": "^15.5.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "rc-steps": "^6.0.1", "react": "^19.1.1", "react-apple-login": "^1.1.6", "react-apple-signin-auth": "^1.1.2", "react-aria-components": "^1.12.2", "react-collapsed": "^4.2.0", "react-currency-input-field": "^3.10.0", "react-datepicker": "^8.7.0", "react-day-picker": "^9.11.0", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-google-places-autocomplete": "^4.1.0", "react-hook-form": "^7.63.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-modal": "^3.16.3", "react-multi-carousel": "^2.8.6", "react-qr-code": "^2.0.18", "react-query-kit": "^3.3.2", "react-quill-new": "^3.6.0", "react-tabs": "^6.1.0", "recharts": "^3.2.1", "stripe": "^18.5.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.1.0", "validator": "^13.15.15", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/compat": "^1.4.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.36.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.13", "@tanstack/eslint-plugin-query": "^5.91.0", "@tanstack/react-query-devtools": "^5.90.2", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/lodash": "^4.17.20", "@types/react": "^19.1.15", "@types/react-dom": "^19.1.9", "@types/react-modal": "^3.16.3", "@types/react-scroll": "^1.8.10", "@types/validator": "^13.15.3", "@typescript-eslint/eslint-plugin": "^8.44.1", "@typescript-eslint/parser": "^8.44.1", "eslint": "^9.36.0", "eslint-config-next": "^15.5.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^16.4.0", "husky": "^9.1.7", "jest": "^30.2.0", "lint-staged": "^16.2.3", "next-router-mock": "^1.0.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.13", "typescript": "^5.9.2"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint", "prettier -w"], "**/*.{json,css,scss,md,webmanifest}": ["prettier -w"]}, "packageManager": "pnpm@10.17.1"}